import React from 'react'
import styled from 'styled-components'
import NavButton from './NavButton'
import type { NavButtonVariant } from './NavButton'

export interface NavBarItem {
  key: string
  label: string
  disabled?: boolean
}

export interface NavBarProps {
  items: NavBarItem[]
  activeKey: string
  variant?: NavButtonVariant
  gap?: string
  className?: string
  style?: React.CSSProperties
  onChange?: (key: string) => void
}

const StyledNavBar = styled.div<{ gap: string }>`
  display: flex;
  align-items: center;
  gap: ${({ gap }) => gap};
`

const NavBar: React.FC<NavBarProps> = ({
  items,
  activeKey,
  variant = 'filled',
  gap = '6px',
  className,
  style,
  onChange,
}) => {
  const handleItemClick = (key: string) => {
    if (onChange) {
      onChange(key)
    }
  }

  return (
    <StyledNavBar gap={gap} className={className} style={style}>
      {items.map((item) => (
        <NavButton
          key={item.key}
          variant={variant}
          active={activeKey === item.key}
          disabled={item.disabled}
          onClick={() => handleItemClick(item.key)}
        >
          {item.label}
        </NavButton>
      ))}
    </StyledNavBar>
  )
}

export default NavBar
