import React from 'react'
import styled from 'styled-components'

export type NavButtonVariant = 'filled' | 'text' | 'outlined'

export interface NavButtonProps {
  variant?: NavButtonVariant
  active?: boolean
  disabled?: boolean
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}

const NAV_BUTTON_VARIANTS = {
  filled: {
    normal: {
      background: '#F2F4F7',
      color: '#1F2733',
      border: 'none',
    },
    hover: {
      background: '#E6E8EC',
      color: '#237FFA',
      border: 'none',
    },
    active: {
      background: '#E2F3FE',
      color: '#237FFA',
      border: 'none',
    },
  },
  text: {
    normal: {
      background: 'transparent',
      color: '#9299A6',
      border: 'none',
    },
    hover: {
      background: 'transparent',
      color: '#237FFA',
      border: 'none',
    },
    active: {
      background: 'transparent',
      color: '#237FFA',
      border: 'none',
    },
  },
  outlined: {
    normal: {
      background: '#FFFFFF',
      color: '#5F6A7A',
      border: '1px solid #EBEDF0',
    },
    hover: {
      background: '#F6F7F9',
      color: '#237FFA',
      border: '1px solid #EBEDF0',
    },
    active: {
      background: '#E2F3FE',
      color: '#237FFA',
      border: '1px solid #BDE2FF',
    },
  },
}

const StyledNavButton = styled.button<{
  variant: NavButtonVariant
  active: boolean
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  margin: 0 3px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  user-select: none;
  
  ${({ variant, active }) => {
    const config = NAV_BUTTON_VARIANTS[variant]
    const state = active ? config.active : config.normal
    return `
      background: ${state.background};
      color: ${state.color};
      border: ${state.border};
    `
  }}
  
  &:hover:not(:disabled) {
    ${({ variant }) => {
      const config = NAV_BUTTON_VARIANTS[variant].hover
      return `
        background: ${config.background};
        color: ${config.color};
        border: ${config.border};
      `
    }}
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const NavButton: React.FC<NavButtonProps> = ({
  variant = 'filled',
  active = false,
  disabled = false,
  children,
  className,
  style,
  onClick,
}) => {
  return (
    <StyledNavButton
      variant={variant}
      active={active}
      disabled={disabled}
      className={className}
      style={style}
      onClick={onClick}
    >
      {children}
    </StyledNavButton>
  )
}

export default NavButton
