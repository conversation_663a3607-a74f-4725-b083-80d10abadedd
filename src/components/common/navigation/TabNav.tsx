import React from 'react'
import styled from 'styled-components'

export interface TabItem {
  key: string
  label: string
  disabled?: boolean
  icon?: React.ReactNode
}

export interface TabNavProps {
  items: TabItem[]
  activeKey: string
  size?: 'small' | 'medium' | 'large'
  type?: 'line' | 'card'
  className?: string
  style?: React.CSSProperties
  onChange?: (key: string) => void
}

const TAB_SIZES = {
  small: {
    padding: '4px 8px',
    fontSize: '12px',
    lineHeight: '16px',
  },
  medium: {
    padding: '8px 16px',
    fontSize: '14px',
    lineHeight: '20px',
  },
  large: {
    padding: '12px 20px',
    fontSize: '16px',
    lineHeight: '24px',
  },
}

const StyledTabNav = styled.div<{ type: 'line' | 'card' }>`
  display: flex;
  align-items: center;
  
  ${({ type }) =>
    type === 'card' &&
    `
    background: #F6F7F9;
    border-radius: 6px;
    padding: 2px;
  `}
`

const StyledTabItem = styled.div<{
  active: boolean
  size: 'small' | 'medium' | 'large'
  type: 'line' | 'card'
  disabled: boolean
}>`
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  position: relative;
  
  ${({ size }) => {
    const sizeConfig = TAB_SIZES[size]
    return `
      padding: ${sizeConfig.padding};
      font-size: ${sizeConfig.fontSize};
      line-height: ${sizeConfig.lineHeight};
    `
  }}
  
  ${({ type, active }) => {
    if (type === 'card') {
      return `
        border-radius: 4px;
        font-weight: ${active ? '500' : 'normal'};
        color: ${active ? '#000' : '#5F6A7A'};
        background-color: ${active ? '#fff' : 'transparent'};
      `
    } else {
      return `
        font-weight: ${active ? '500' : 'normal'};
        color: ${active ? '#237FFA' : '#5F6A7A'};
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background-color: ${active ? '#237FFA' : 'transparent'};
          transition: background-color 0.2s ease-in-out;
        }
      `
    }
  }}
  
  ${({ disabled }) =>
    disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}
  
  &:hover:not([disabled]) {
    color: #237FFA;
  }
`

const TabNav: React.FC<TabNavProps> = ({
  items,
  activeKey,
  size = 'medium',
  type = 'line',
  className,
  style,
  onChange,
}) => {
  const handleItemClick = (key: string, disabled?: boolean) => {
    if (!disabled && onChange) {
      onChange(key)
    }
  }

  return (
    <StyledTabNav type={type} className={className} style={style}>
      {items.map((item) => (
        <StyledTabItem
          key={item.key}
          active={activeKey === item.key}
          size={size}
          type={type}
          disabled={!!item.disabled}
          onClick={() => handleItemClick(item.key, item.disabled)}
        >
          {item.icon}
          {item.label}
        </StyledTabItem>
      ))}
    </StyledTabNav>
  )
}

export default TabNav
