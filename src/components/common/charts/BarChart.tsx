import React, { forwardRef } from 'react'
import Chart, { ChartProps, ChartRef, ChartDataItem } from './Chart'

export interface BarChartDataItem extends ChartDataItem {
  target?: number
}

export interface BarChartProps extends Omit<ChartProps, 'type' | 'data'> {
  data: {
    name?: string
    dataList: BarChartDataItem[]
  }
  direction?: 'vertical' | 'horizontal'
  showGrid?: boolean
  showTarget?: boolean
  barWidth?: string | number
}

const BarChart = forwardRef<ChartRef, BarChartProps>(({
  data,
  direction = 'vertical',
  showGrid = true,
  showTarget = false,
  barWidth,
  options = {},
  ...props
}, ref) => {
  const isVertical = direction === 'vertical'
  
  // 生成柱状图特定配置
  const barOptions = {
    grid: {
      show: showGrid,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: isVertical ? 'category' : 'value',
      data: isVertical ? data.dataList.map(item => item.name) : undefined,
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: isVertical ? 'value' : 'category',
      data: !isVertical ? data.dataList.map(item => item.name) : undefined,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#F0F0F0',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        type: 'bar',
        data: data.dataList.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
          },
        })),
        barWidth,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
      // 目标线系列
      ...(showTarget ? [{
        type: 'line',
        name: '目标',
        data: data.dataList.map(item => item.target || 0),
        lineStyle: {
          color: '#FF4A4A',
          type: 'dashed',
          width: 2,
        },
        symbol: 'none',
        yAxisIndex: 0,
      }] : []),
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        if (Array.isArray(params)) {
          const dataPoint = params[0]
          const item = data.dataList.find(d => d.name === dataPoint.name)
          let tooltip = `${dataPoint.name}: ${dataPoint.value}`
          
          if (showTarget && item?.target) {
            tooltip += `<br/>目标: ${item.target}`
          }
          
          return tooltip
        }
        return ''
      },
    },
    ...options,
  }

  return (
    <Chart
      ref={ref}
      type="bar"
      data={data}
      options={barOptions}
      {...props}
    />
  )
})

BarChart.displayName = 'BarChart'

export default BarChart
