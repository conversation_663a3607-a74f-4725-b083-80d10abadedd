import React, { forwardRef } from 'react'
import Chart, { ChartProps, ChartRef, ChartDataItem } from './Chart'

export interface LineChartDataItem extends ChartDataItem {
  target?: number
}

export interface LineChartProps extends Omit<ChartProps, 'type' | 'data'> {
  data: {
    name?: string
    dataList: LineChartDataItem[]
  } | {
    name?: string
    dataList: LineChartDataItem[]
  }[]
  smooth?: boolean
  showArea?: boolean
  showPoints?: boolean
  showGrid?: boolean
}

const LineChart = forwardRef<ChartRef, LineChartProps>(({
  data,
  smooth = true,
  showArea = false,
  showPoints = true,
  showGrid = true,
  options = {},
  ...props
}, ref) => {
  const dataArray = Array.isArray(data) ? data : [data]
  const xAxisData = dataArray[0]?.dataList?.map(item => item.name) || []
  
  // 生成折线图特定配置
  const lineOptions = {
    grid: {
      show: showGrid,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#F0F0F0',
          type: 'dashed',
        },
      },
    },
    series: dataArray.map((seriesData, index) => ({
      type: 'line',
      name: seriesData.name || `系列${index + 1}`,
      data: seriesData.dataList.map(item => item.value),
      smooth,
      symbol: showPoints ? 'circle' : 'none',
      symbolSize: 6,
      lineStyle: {
        width: 2,
      },
      areaStyle: showArea ? {
        opacity: 0.3,
      } : undefined,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    })),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params: any) => {
        if (Array.isArray(params) && params.length > 0) {
          const axisValue = params[0].axisValue
          let tooltip = `${axisValue}<br/>`
          
          params.forEach((param: any) => {
            tooltip += `${param.seriesName}: ${param.value}<br/>`
          })
          
          return tooltip
        }
        return ''
      },
    },
    legend: {
      show: dataArray.length > 1,
      top: 10,
      textStyle: {
        color: '#666666',
        fontSize: 12,
      },
    },
    ...options,
  }

  return (
    <Chart
      ref={ref}
      type="line"
      data={data}
      options={lineOptions}
      {...props}
    />
  )
})

LineChart.displayName = 'LineChart'

export default LineChart
