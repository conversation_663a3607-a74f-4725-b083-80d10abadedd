import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

export type ChartType = 'pie' | 'bar' | 'line' | 'scatter' | 'radar'

export interface ChartDataItem {
  name: string
  value: number
  color?: string
  [key: string]: any
}

export interface ChartData {
  name?: string
  dataList: ChartDataItem[]
}

export interface ChartTheme {
  color: string[] | Record<string, string | string[]>
  icons?: string[]
}

export interface ChartProps {
  type: ChartType
  data: ChartData | ChartData[]
  theme?: ChartTheme
  width?: string | number
  height?: string | number
  className?: string
  style?: React.CSSProperties
  options?: Partial<EChartsOption>
  onClick?: (params: any) => void
  onReady?: (chart: ECharts) => void
}

export interface ChartRef {
  getChart: () => ECharts | null
  resize: () => void
  dispose: () => void
}

const Chart = forwardRef<ChartRef, ChartProps>(({
  type,
  data,
  theme,
  width = '100%',
  height = '300px',
  className,
  style,
  options = {},
  onClick,
  onReady,
}, ref) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstanceRef = useRef<ECharts | null>(null)
  const [isReady, setIsReady] = useState(false)

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    getChart: () => chartInstanceRef.current,
    resize: () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize()
      }
    },
    dispose: () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose()
        chartInstanceRef.current = null
      }
    },
  }))

  // 生成基础配置
  const generateBaseOption = (): EChartsOption => {
    const baseOption: EChartsOption = {
      tooltip: {
        trigger: 'item',
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          color: '#333333',
        },
      },
      legend: {
        show: true,
        orient: 'vertical',
        right: 10,
        top: 'center',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#666666',
          fontSize: 12,
        },
      },
    }

    // 根据图表类型设置特定配置
    switch (type) {
      case 'pie':
        return {
          ...baseOption,
          series: [{
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            data: Array.isArray(data) ? data[0]?.dataList || [] : data.dataList || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          }],
        }

      case 'bar':
        const barData = Array.isArray(data) ? data[0] : data
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: barData?.dataList?.map(item => item.name) || [],
          },
          yAxis: {
            type: 'value',
          },
          series: [{
            type: 'bar',
            data: barData?.dataList?.map(item => item.value) || [],
          }],
        }

      case 'line':
        const lineData = Array.isArray(data) ? data[0] : data
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: lineData?.dataList?.map(item => item.name) || [],
          },
          yAxis: {
            type: 'value',
          },
          series: [{
            type: 'line',
            data: lineData?.dataList?.map(item => item.value) || [],
            smooth: true,
          }],
        }

      default:
        return baseOption
    }
  }

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return

    // 创建图表实例
    chartInstanceRef.current = echarts.init(chartRef.current)

    // 设置配置
    const baseOption = generateBaseOption()
    const finalOption = { ...baseOption, ...options }

    // 应用主题颜色
    if (theme?.color) {
      if (Array.isArray(theme.color)) {
        finalOption.color = theme.color
      } else if (typeof theme.color === 'object') {
        // 处理对象形式的颜色配置
        finalOption.color = Object.values(theme.color).flat()
      }
    }

    chartInstanceRef.current.setOption(finalOption)

    // 绑定点击事件
    if (onClick) {
      chartInstanceRef.current.on('click', onClick)
    }

    setIsReady(true)

    // 调用准备完成回调
    if (onReady) {
      onReady(chartInstanceRef.current)
    }

    // 清理函数
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose()
        chartInstanceRef.current = null
      }
    }
  }, [])

  // 数据变化时更新图表
  useEffect(() => {
    if (!chartInstanceRef.current || !isReady) return

    const baseOption = generateBaseOption()
    const finalOption = { ...baseOption, ...options }

    // 应用主题颜色
    if (theme?.color) {
      if (Array.isArray(theme.color)) {
        finalOption.color = theme.color
      } else if (typeof theme.color === 'object') {
        finalOption.color = Object.values(theme.color).flat()
      }
    }

    chartInstanceRef.current.setOption(finalOption, true)
  }, [data, theme, options, type, isReady])

  // 窗口大小变化时重新调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize()
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 处理尺寸
  const chartStyle: React.CSSProperties = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
    ...style,
  }

  return (
    <div
      ref={chartRef}
      className={className}
      style={chartStyle}
    />
  )
})

Chart.displayName = 'Chart'

export default Chart
