import React, { forwardRef } from 'react'
import Chart, { ChartProps, ChartRef, ChartDataItem } from './Chart'

export interface PieChartDataItem extends ChartDataItem {
  details?: string[]
}

export interface PieChartProps extends Omit<ChartProps, 'type' | 'data'> {
  data: {
    name?: string
    dataList: PieChartDataItem[]
  }
  radius?: [string, string]
  center?: [string, string]
  showLegend?: boolean
  legendPosition?: 'right' | 'bottom' | 'top' | 'left'
}

const PieChart = forwardRef<ChartRef, PieChartProps>(({
  data,
  radius = ['40%', '70%'],
  center = ['40%', '50%'],
  showLegend = true,
  legendPosition = 'right',
  options = {},
  ...props
}, ref) => {
  // 生成饼图特定配置
  const pieOptions = {
    legend: {
      show: showLegend,
      orient: legendPosition === 'right' || legendPosition === 'left' ? 'vertical' : 'horizontal',
      [legendPosition]: legendPosition === 'right' || legendPosition === 'left' ? 10 : 'center',
      [legendPosition === 'right' || legendPosition === 'left' ? 'top' : 'bottom']: 
        legendPosition === 'right' || legendPosition === 'left' ? 'center' : 10,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#666666',
        fontSize: 12,
      },
    },
    series: [{
      type: 'pie',
      radius,
      center,
      data: data.dataList,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    }],
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const { name, value, percent } = params
        const item = data.dataList.find(d => d.name === name)
        let tooltip = `${name}: ${value} (${percent}%)`
        
        if (item?.details && item.details.length > 0) {
          tooltip += '<br/>' + item.details.join('<br/>')
        }
        
        return tooltip
      },
    },
    ...options,
  }

  return (
    <Chart
      ref={ref}
      type="pie"
      data={data}
      options={pieOptions}
      {...props}
    />
  )
})

PieChart.displayName = 'PieChart'

export default PieChart
