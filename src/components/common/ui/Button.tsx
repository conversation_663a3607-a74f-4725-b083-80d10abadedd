import React from 'react'
import styled from 'styled-components'

export type ButtonVariant = 'primary' | 'secondary' | 'text' | 'filled'
export type ButtonSize = 'small' | 'medium' | 'large'

export interface ButtonProps {
  variant?: ButtonVariant
  size?: ButtonSize
  active?: boolean
  disabled?: boolean
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}

const BUTTON_VARIANTS = {
  primary: {
    normal: {
      background: '#237FFA',
      color: '#FFFFFF',
      border: '1px solid #237FFA',
    },
    hover: {
      background: '#1E6FE0',
      color: '#FFFFFF',
      border: '1px solid #1E6FE0',
    },
    active: {
      background: '#E2F3FE',
      color: '#237FFA',
      border: '1px solid #BDE2FF',
    },
  },
  secondary: {
    normal: {
      background: '#FFFFFF',
      color: '#5F6A7A',
      border: '1px solid #EBEDF0',
    },
    hover: {
      background: '#F6F7F9',
      color: '#237FFA',
      border: '1px solid #EBEDF0',
    },
    active: {
      background: '#E2F3FE',
      color: '#237FFA',
      border: '1px solid #BDE2FF',
    },
  },
  text: {
    normal: {
      background: 'transparent',
      color: '#9299A6',
      border: 'none',
    },
    hover: {
      background: 'transparent',
      color: '#237FFA',
      border: 'none',
    },
    active: {
      background: 'transparent',
      color: '#237FFA',
      border: 'none',
    },
  },
  filled: {
    normal: {
      background: '#F2F4F7',
      color: '#1F2733',
      border: 'none',
    },
    hover: {
      background: '#E6E8EC',
      color: '#237FFA',
      border: 'none',
    },
    active: {
      background: '#E2F3FE',
      color: '#237FFA',
      border: 'none',
    },
  },
}

const BUTTON_SIZES = {
  small: {
    padding: '4px 8px',
    fontSize: '12px',
    lineHeight: '16px',
    borderRadius: '4px',
  },
  medium: {
    padding: '6px 12px',
    fontSize: '14px',
    lineHeight: '20px',
    borderRadius: '6px',
  },
  large: {
    padding: '8px 16px',
    fontSize: '16px',
    lineHeight: '24px',
    borderRadius: '8px',
  },
}

const StyledButton = styled.button<{
  variant: ButtonVariant
  size: ButtonSize
  active: boolean
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  user-select: none;
  
  ${({ variant, active }) => {
    const config = BUTTON_VARIANTS[variant]
    const state = active ? config.active : config.normal
    return `
      background: ${state.background};
      color: ${state.color};
      border: ${state.border};
    `
  }}
  
  ${({ size }) => {
    const sizeConfig = BUTTON_SIZES[size]
    return `
      padding: ${sizeConfig.padding};
      font-size: ${sizeConfig.fontSize};
      line-height: ${sizeConfig.lineHeight};
      border-radius: ${sizeConfig.borderRadius};
    `
  }}
  
  &:hover:not(:disabled) {
    ${({ variant }) => {
      const config = BUTTON_VARIANTS[variant].hover
      return `
        background: ${config.background};
        color: ${config.color};
        border: ${config.border};
      `
    }}
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const Button: React.FC<ButtonProps> = ({
  variant = 'secondary',
  size = 'medium',
  active = false,
  disabled = false,
  children,
  className,
  style,
  onClick,
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      active={active}
      disabled={disabled}
      className={className}
      style={style}
      onClick={onClick}
    >
      {children}
    </StyledButton>
  )
}

export default Button
