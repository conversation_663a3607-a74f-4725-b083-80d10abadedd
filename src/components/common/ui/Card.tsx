import React from 'react'
import styled from 'styled-components'

export type CardVariant = 'default' | 'bordered' | 'elevated' | 'outlined'

export interface CardProps {
  variant?: CardVariant
  padding?: string
  borderRadius?: string
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}

const CARD_VARIANTS = {
  default: {
    border: '1px solid #EBEDF0',
    boxShadow: 'none',
    background: '#FFFFFF',
  },
  bordered: {
    border: '2px solid #FF6B35',
    boxShadow: 'none',
    background: '#FFFFFF',
  },
  elevated: {
    border: '1px solid #EBEDF0',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    background: '#FFFFFF',
  },
  outlined: {
    border: '1px solid #D1D5DB',
    boxShadow: 'none',
    background: 'transparent',
  },
}

const StyledCard = styled.div<{
  variant: CardVariant
  padding: string
  borderRadius: string
  clickable: boolean
}>`
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease-in-out;
  
  ${({ variant }) => {
    const config = CARD_VARIANTS[variant]
    return `
      border: ${config.border};
      box-shadow: ${config.boxShadow};
      background: ${config.background};
    `
  }}
  
  padding: ${({ padding }) => padding};
  border-radius: ${({ borderRadius }) => borderRadius};
  
  ${({ clickable }) =>
    clickable &&
    `
    cursor: pointer;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  `}
`

const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = '20px 16px',
  borderRadius = '12px',
  children,
  className,
  style,
  onClick,
}) => {
  return (
    <StyledCard
      variant={variant}
      padding={padding}
      borderRadius={borderRadius}
      clickable={!!onClick}
      className={className}
      style={style}
      onClick={onClick}
    >
      {children}
    </StyledCard>
  )
}

export default Card
