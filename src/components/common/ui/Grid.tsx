import React from 'react'
import styled from 'styled-components'

export interface GridProps {
  columns?: string | number
  rows?: string | number
  gap?: string
  columnGap?: string
  rowGap?: string
  alignItems?: string
  justifyItems?: string
  alignContent?: string
  justifyContent?: string
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const StyledGrid = styled.div<{
  columns: string
  rows?: string
  gap?: string
  columnGap?: string
  rowGap?: string
  alignItems?: string
  justifyItems?: string
  alignContent?: string
  justifyContent?: string
}>`
  display: grid;
  
  grid-template-columns: ${({ columns }) => columns};
  ${({ rows }) => rows && `grid-template-rows: ${rows};`}
  
  ${({ gap }) => gap && `gap: ${gap};`}
  ${({ columnGap }) => columnGap && `column-gap: ${columnGap};`}
  ${({ rowGap }) => rowGap && `row-gap: ${rowGap};`}
  
  ${({ alignItems }) => alignItems && `align-items: ${alignItems};`}
  ${({ justifyItems }) => justifyItems && `justify-items: ${justifyItems};`}
  ${({ alignContent }) => alignContent && `align-content: ${alignContent};`}
  ${({ justifyContent }) => justifyContent && `justify-content: ${justifyContent};`}
`

const Grid: React.FC<GridProps> = ({
  columns = '1fr',
  rows,
  gap,
  columnGap,
  rowGap,
  alignItems,
  justifyItems,
  alignContent,
  justifyContent,
  children,
  className,
  style,
}) => {
  // 处理 columns 参数
  const gridColumns = typeof columns === 'number' 
    ? `repeat(${columns}, 1fr)` 
    : columns

  return (
    <StyledGrid
      columns={gridColumns}
      rows={rows}
      gap={gap}
      columnGap={columnGap}
      rowGap={rowGap}
      alignItems={alignItems}
      justifyItems={justifyItems}
      alignContent={alignContent}
      justifyContent={justifyContent}
      className={className}
      style={style}
    >
      {children}
    </StyledGrid>
  )
}

export default Grid
