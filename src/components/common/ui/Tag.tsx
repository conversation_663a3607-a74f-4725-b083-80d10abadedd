import React from 'react'
import styled from 'styled-components'

export type TagVariant = 'risk' | 'level' | 'status' | 'default'
export type TagSize = 'small' | 'medium' | 'large'

export interface TagProps {
  variant?: TagVariant
  size?: TagSize
  type?: string // 具体的类型，如 'high', 'blocker' 等
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

// 风险标签颜色配置
const RISK_COLORS = {
  high: {
    color: '#B32D36',
    background: '#FEF1EE',
  },
  medium: {
    color: '#875100',
    background: '#FEFCE9',
  },
  low: {
    color: '#006BB3',
    background: '#ECFCFE',
  },
}

// 等级标签颜色配置
const LEVEL_COLORS = {
  blocker: {
    color: '#FE7940',
    background: '#FFF0E9',
  },
  critical: {
    color: '#FE9561',
    background: '#FFF2EC',
  },
  major: {
    color: '#FEC833',
    background: '#FFF8E5',
  },
  minor: {
    color: '#FEE789',
    background: '#FFFBED',
  },
}

// 状态标签颜色配置
const STATUS_COLORS = {
  success: {
    color: '#00BB60',
    background: '#F0F9F4',
  },
  warning: {
    color: '#FF8800',
    background: '#FFF7E6',
  },
  error: {
    color: '#FF4A4A',
    background: '#FFF1F0',
  },
  info: {
    color: '#237FFA',
    background: '#E6F7FF',
  },
}

// 默认颜色配置
const DEFAULT_COLORS = {
  default: {
    color: '#5F6A7A',
    background: '#F6F7F9',
  },
}

// 尺寸配置
const SIZES = {
  small: {
    padding: '1px 6px',
    fontSize: '12px',
    lineHeight: '16px',
  },
  medium: {
    padding: '2px 8px',
    fontSize: '14px',
    lineHeight: '20px',
  },
  large: {
    padding: '4px 12px',
    fontSize: '16px',
    lineHeight: '24px',
  },
}

const getColorConfig = (variant: TagVariant, type: string) => {
  switch (variant) {
    case 'risk':
      return RISK_COLORS[type as keyof typeof RISK_COLORS] || DEFAULT_COLORS.default
    case 'level':
      return LEVEL_COLORS[type as keyof typeof LEVEL_COLORS] || DEFAULT_COLORS.default
    case 'status':
      return STATUS_COLORS[type as keyof typeof STATUS_COLORS] || DEFAULT_COLORS.default
    default:
      return DEFAULT_COLORS.default
  }
}

const StyledTag = styled.div<{
  variant: TagVariant
  size: TagSize
  type: string
}>`
  display: inline-block;
  font-weight: normal;
  border-radius: 4px;
  white-space: nowrap;
  
  ${({ variant, type }) => {
    const colors = getColorConfig(variant, type)
    return `
      color: ${colors.color};
      background: ${colors.background};
    `
  }}
  
  ${({ size }) => {
    const sizeConfig = SIZES[size]
    return `
      padding: ${sizeConfig.padding};
      font-size: ${sizeConfig.fontSize};
      line-height: ${sizeConfig.lineHeight};
    `
  }}
`

const Tag: React.FC<TagProps> = ({
  variant = 'default',
  size = 'medium',
  type = 'default',
  children,
  className,
  style,
}) => {
  return (
    <StyledTag
      variant={variant}
      size={size}
      type={type}
      className={className}
      style={style}
    >
      {children}
    </StyledTag>
  )
}

export default Tag
