import React from 'react'
import styled from 'styled-components'

export interface DividerProps {
  orientation?: 'horizontal' | 'vertical'
  width?: string
  height?: string
  color?: string
  margin?: string
  style?: 'solid' | 'dashed' | 'dotted'
  className?: string
}

const StyledDivider = styled.div<{
  orientation: 'horizontal' | 'vertical'
  width: string
  height: string
  color: string
  margin: string
  dividerStyle: string
}>`
  flex: none;
  
  ${({ orientation, width, height, color, dividerStyle }) => {
    if (orientation === 'horizontal') {
      return `
        width: ${width};
        height: 1px;
        border-top: 1px ${dividerStyle} ${color};
      `
    } else {
      return `
        width: 1px;
        height: ${height};
        border-left: 1px ${dividerStyle} ${color};
      `
    }
  }}
  
  margin: ${({ margin }) => margin};
`

const Divider: React.FC<DividerProps> = ({
  orientation = 'vertical',
  width = '100%',
  height = '20px',
  color = '#DFE2E8',
  margin = '0',
  style = 'solid',
  className,
}) => {
  return (
    <StyledDivider
      orientation={orientation}
      width={width}
      height={height}
      color={color}
      margin={margin}
      dividerStyle={style}
      className={className}
    />
  )
}

export default Divider
