import React from 'react'
import styled from 'styled-components'
import { Card, Tag } from '../ui'
import type { CardProps } from '../ui'

export interface MetricItem {
  label: string
  value: string | number
  unit?: string
  color?: string
  trend?: 'up' | 'down' | 'stable'
  trendValue?: string | number
}

export interface MetricCardProps extends Omit<CardProps, 'children'> {
  title: string
  metrics: MetricItem[]
  layout?: 'grid' | 'list'
  columns?: number
  showBorder?: boolean
  size?: 'small' | 'medium' | 'large'
}

const SIZE_CONFIG = {
  small: {
    padding: '12px',
    titleSize: '14px',
    valueSize: '18px',
    labelSize: '12px',
    gap: '8px',
  },
  medium: {
    padding: '16px',
    titleSize: '16px',
    valueSize: '24px',
    labelSize: '14px',
    gap: '12px',
  },
  large: {
    padding: '20px',
    titleSize: '18px',
    valueSize: '32px',
    labelSize: '16px',
    gap: '16px',
  },
}

const TREND_COLORS = {
  up: '#00BB60',
  down: '#FF4A4A',
  stable: '#9299A6',
}

const StyledMetricCard = styled(Card)<{ size: keyof typeof SIZE_CONFIG }>`
  padding: ${({ size }) => SIZE_CONFIG[size].padding};
`

const CardTitle = styled.div<{ size: keyof typeof SIZE_CONFIG }>`
  font-size: ${({ size }) => SIZE_CONFIG[size].titleSize};
  font-weight: 500;
  color: #1F2733;
  margin-bottom: ${({ size }) => SIZE_CONFIG[size].gap};
  line-height: 1.4;
`

const MetricsContainer = styled.div<{
  layout: 'grid' | 'list'
  columns: number
  gap: string
}>`
  display: ${({ layout }) => layout === 'grid' ? 'grid' : 'flex'};
  ${({ layout, columns }) => layout === 'grid' && `grid-template-columns: repeat(${columns}, 1fr);`}
  ${({ layout }) => layout === 'list' && 'flex-direction: column;'}
  gap: ${({ gap }) => gap};
`

const MetricItem = styled.div<{
  showBorder: boolean
  size: keyof typeof SIZE_CONFIG
}>`
  display: flex;
  flex-direction: column;
  gap: 4px;
  ${({ showBorder }) => showBorder && `
    padding: 12px;
    border: 1px solid #F0F0F0;
    border-radius: 6px;
  `}
`

const MetricLabel = styled.div<{ size: keyof typeof SIZE_CONFIG }>`
  font-size: ${({ size }) => SIZE_CONFIG[size].labelSize};
  font-weight: 400;
  color: #5F6A7A;
  line-height: 1.4;
`

const MetricValueSection = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  gap: 8px;
`

const MetricValue = styled.div<{
  size: keyof typeof SIZE_CONFIG
  color?: string
}>`
  font-size: ${({ size }) => SIZE_CONFIG[size].valueSize};
  font-weight: 600;
  color: ${({ color }) => color || '#1F2733'};
  line-height: 1;
  font-family: 'MiSans-Medium', 'PingFang SC', 'Microsoft YaHei', sans-serif;
`

const MetricUnit = styled.span`
  font-size: 0.7em;
  font-weight: 400;
  margin-left: 2px;
`

const TrendIndicator = styled.div<{ trend: string }>`
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: ${({ trend }) => TREND_COLORS[trend as keyof typeof TREND_COLORS]};
  font-weight: 500;
`

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  metrics,
  layout = 'grid',
  columns = 2,
  showBorder = false,
  size = 'medium',
  ...cardProps
}) => {
  const renderTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      case 'stable':
        return '→'
      default:
        return null
    }
  }

  return (
    <StyledMetricCard size={size} {...cardProps}>
      <CardTitle size={size}>{title}</CardTitle>
      
      <MetricsContainer
        layout={layout}
        columns={columns}
        gap={SIZE_CONFIG[size].gap}
      >
        {metrics.map((metric, index) => (
          <MetricItem key={index} showBorder={showBorder} size={size}>
            <MetricLabel size={size}>{metric.label}</MetricLabel>
            <MetricValueSection>
              <MetricValue size={size} color={metric.color}>
                {metric.value}
                {metric.unit && <MetricUnit>{metric.unit}</MetricUnit>}
              </MetricValue>
              {metric.trend && (
                <TrendIndicator trend={metric.trend}>
                  {renderTrendIcon(metric.trend)}
                  {metric.trendValue}
                </TrendIndicator>
              )}
            </MetricValueSection>
          </MetricItem>
        ))}
      </MetricsContainer>
    </StyledMetricCard>
  )
}

export default MetricCard
