import React from 'react'
import styled from 'styled-components'
import { Card } from '../ui'
import type { CardProps } from '../ui'

export interface StatCardProps extends Omit<CardProps, 'children'> {
  title: string
  value: string | number
  target?: string | number
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  trendValue?: string | number
  status?: 'success' | 'warning' | 'error' | 'normal'
  icon?: React.ReactNode
  extra?: React.ReactNode
  loading?: boolean
}

const STATUS_COLORS = {
  success: '#00BB60',
  warning: '#FF8800',
  error: '#FF4A4A',
  normal: '#1F2733',
}

const TREND_COLORS = {
  up: '#00BB60',
  down: '#FF4A4A',
  stable: '#9299A6',
}

const StyledStatCard = styled(Card)`
  min-height: 120px;
  position: relative;
`

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`

const Title = styled.div`
  font-size: 14px;
  font-weight: 400;
  color: #5F6A7A;
  line-height: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
`

const ValueSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
`

const Value = styled.div<{ status: string }>`
  font-size: 32px;
  font-weight: 600;
  line-height: 1;
  color: ${({ status }) => STATUS_COLORS[status as keyof typeof STATUS_COLORS]};
  font-family: 'MiSans-Medium', 'PingFang SC', 'Microsoft YaHei', sans-serif;
`

const Unit = styled.span`
  font-size: 16px;
  font-weight: 400;
  margin-left: 4px;
`

const MetaInfo = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 14px;
  color: #5F6A7A;
`

const Target = styled.span`
  font-weight: 500;
`

const Trend = styled.div<{ trend: string }>`
  display: flex;
  align-items: center;
  gap: 4px;
  color: ${({ trend }) => TREND_COLORS[trend as keyof typeof TREND_COLORS]};
  font-weight: 500;
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
`

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  target,
  unit,
  trend,
  trendValue,
  status = 'normal',
  icon,
  extra,
  loading = false,
  ...cardProps
}) => {
  const renderTrendIcon = () => {
    switch (trend) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      case 'stable':
        return '→'
      default:
        return null
    }
  }

  return (
    <StyledStatCard {...cardProps}>
      {loading && (
        <LoadingOverlay>
          <div>加载中...</div>
        </LoadingOverlay>
      )}
      
      <CardHeader>
        <Title>
          {icon}
          {title}
        </Title>
        {extra}
      </CardHeader>
      
      <ValueSection>
        <Value status={status}>
          {value}
          {unit && <Unit>{unit}</Unit>}
        </Value>
        
        <MetaInfo>
          {target && (
            <Target>
              目标 {target}{unit}
            </Target>
          )}
          
          {trend && (
            <Trend trend={trend}>
              {renderTrendIcon()}
              {trendValue}
            </Trend>
          )}
        </MetaInfo>
      </ValueSection>
    </StyledStatCard>
  )
}

export default StatCard
