import React from 'react'
import styled from 'styled-components'
import { Card } from '../ui'
import type { CardProps } from '../ui'

export interface InfoCardProps extends Omit<CardProps, 'children'> {
  title?: string
  description?: string
  icon?: React.ReactNode
  actions?: React.ReactNode
  children?: React.ReactNode
  layout?: 'vertical' | 'horizontal'
  size?: 'small' | 'medium' | 'large'
}

const SIZE_CONFIG = {
  small: {
    padding: '12px',
    titleSize: '14px',
    descSize: '12px',
    iconSize: '16px',
  },
  medium: {
    padding: '16px',
    titleSize: '16px',
    descSize: '14px',
    iconSize: '20px',
  },
  large: {
    padding: '20px',
    titleSize: '18px',
    descSize: '16px',
    iconSize: '24px',
  },
}

const StyledInfoCard = styled(Card)<{ size: keyof typeof SIZE_CONFIG }>`
  padding: ${({ size }) => SIZE_CONFIG[size].padding};
`

const CardContent = styled.div<{ layout: 'vertical' | 'horizontal' }>`
  display: flex;
  flex-direction: ${({ layout }) => layout === 'vertical' ? 'column' : 'row'};
  gap: 12px;
  align-items: ${({ layout }) => layout === 'horizontal' ? 'flex-start' : 'stretch'};
`

const IconSection = styled.div<{ size: keyof typeof SIZE_CONFIG }>`
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${({ size }) => SIZE_CONFIG[size].iconSize};
  height: ${({ size }) => SIZE_CONFIG[size].iconSize};
  
  svg, img {
    width: 100%;
    height: 100%;
  }
`

const ContentSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
`

const Title = styled.div<{ size: keyof typeof SIZE_CONFIG }>`
  font-size: ${({ size }) => SIZE_CONFIG[size].titleSize};
  font-weight: 500;
  color: #1F2733;
  line-height: 1.4;
`

const Description = styled.div<{ size: keyof typeof SIZE_CONFIG }>`
  font-size: ${({ size }) => SIZE_CONFIG[size].descSize};
  font-weight: 400;
  color: #5F6A7A;
  line-height: 1.5;
`

const ChildrenSection = styled.div`
  margin-top: 8px;
`

const ActionsSection = styled.div`
  flex: none;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
`

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  description,
  icon,
  actions,
  children,
  layout = 'horizontal',
  size = 'medium',
  ...cardProps
}) => {
  return (
    <StyledInfoCard size={size} {...cardProps}>
      <CardContent layout={layout}>
        {icon && (
          <IconSection size={size}>
            {icon}
          </IconSection>
        )}
        
        <ContentSection>
          {title && <Title size={size}>{title}</Title>}
          {description && <Description size={size}>{description}</Description>}
          {children && <ChildrenSection>{children}</ChildrenSection>}
        </ContentSection>
      </CardContent>
      
      {actions && (
        <ActionsSection>
          {actions}
        </ActionsSection>
      )}
    </StyledInfoCard>
  )
}

export default InfoCard
