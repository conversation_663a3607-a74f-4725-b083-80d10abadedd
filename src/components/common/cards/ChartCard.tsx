import React from 'react'
import styled from 'styled-components'
import { Card } from '../ui'
import type { CardProps } from '../ui'

export interface ChartCardProps extends Omit<CardProps, 'children'> {
  title: string
  subtitle?: string
  chart: React.ReactNode
  legend?: React.ReactNode
  extra?: React.ReactNode
  loading?: boolean
  empty?: boolean
  emptyText?: string
  headerBorder?: boolean
}

const StyledChartCard = styled(Card)`
  display: flex;
  flex-direction: column;
  min-height: 200px;
`

const CardHeader = styled.div<{ border: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  ${({ border }) => border && `
    padding-bottom: 16px;
    border-bottom: 1px solid #F0F0F0;
  `}
`

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`

const Title = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #1F2733;
  line-height: 24px;
`

const Subtitle = styled.div`
  font-size: 12px;
  font-weight: 400;
  color: #9299A6;
  line-height: 16px;
`

const ChartContent = styled.div`
  display: flex;
  flex: 1;
  gap: 16px;
  align-items: center;
  min-height: 0;
`

const ChartSection = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
`

const LegendSection = styled.div`
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
`

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #9299A6;
  font-size: 14px;
  min-height: 120px;
`

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  subtitle,
  chart,
  legend,
  extra,
  loading = false,
  empty = false,
  emptyText = '暂无数据',
  headerBorder = false,
  style,
  ...cardProps
}) => {
  return (
    <StyledChartCard style={{ position: 'relative', ...style }} {...cardProps}>
      {loading && (
        <LoadingOverlay>
          <div>加载中...</div>
        </LoadingOverlay>
      )}
      
      <CardHeader border={headerBorder}>
        <TitleSection>
          <Title>{title}</Title>
          {subtitle && <Subtitle>{subtitle}</Subtitle>}
        </TitleSection>
        {extra}
      </CardHeader>
      
      {empty ? (
        <EmptyState>
          <div>📊</div>
          <div>{emptyText}</div>
        </EmptyState>
      ) : (
        <ChartContent>
          <ChartSection>
            {chart}
          </ChartSection>
          {legend && (
            <LegendSection>
              {legend}
            </LegendSection>
          )}
        </ChartContent>
      )}
    </StyledChartCard>
  )
}

export default ChartCard
