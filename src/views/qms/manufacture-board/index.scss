@font-face {
  font-family: MiSans-Medium;
  src: url('https://cdn.cnbj1.fds.api.mi-img.com/scm/qms/work-bench/family/MiSans-Medium.ttf')
    format('truetype');
}

.prd-statistical-radio {
  padding: 2px;
  font-weight: normal;
  color: #5f6a7a;
  background-color: #f6f7f9;
  border-radius: 6px;

  .hi-v4-radio--type-button[data-checked] {
    box-sizing: border-box;
    color: #000;
    background-color: #fff;
    border-radius: 4px;
    font-weight: 500;
  }

  .hi-v4-radio--type-button::after {
    display: none;
  }
}

.prd-board {
  min-width: 1300px;
}

.prd-boardline {
  width: max-content;
  height: auto;
  min-width: 100%;
  background-color: rgb(255 255 255 / 65%);
  overflow: visible hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.prd-boardcell {
  padding-bottom: 20px;
}

.prd-select-more {
  --hi-v4-color-gray-300: #ebedf0;
  --hi-v4-color-gray-700: #5f6a7a;

  &.active {
    --hi-v4-color-gray-300: #bde2ff;
    --hi-v4-color-gray-700: #237ffa;
    --hi-v4-color-static-white: #e2f3fe;
  }
}

$text-color: #1f2733 !default;
$text-color-active: #237ffa !default;
$font-weight-500: 500 !default;
$font-weight-400: 400 !default;
$font-weight-600: 600 !default;
$font-size-24: 24px !default;
$font-size-14: 14px !default;

$bg-color-light: #f2f4f7 !default;
$color-success: #08a351 !default;
$color-fail: #ff5959 !default;
$color-secondary: #9299a6 !default;
$color-tertiary: #5f6a7a !default;
$color-disabled: #d1d1d1 !default;
$color-dashed: #dfe2e8 !default;

$font-family-base: misans-medium, 'PingFang SC', 'Microsoft YaHei', sans-serif !default;

.mfgdi {
  &-header {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1.5px dashed $color-dashed;

    .card-name {
      font-size: $font-size-14;
      font-weight: $font-weight-500;
      color: $text-color;
      line-height: 20px;
      margin-bottom: 4px;
    }

    .value-text {
      font-family: $font-family-base;
      font-size: $font-size-24;
      font-weight: $font-weight-600;
      color: $text-color;
      line-height: 28px;

      &-success {
        color: $color-success;
      }

      &-fail {
        color: $color-fail;
      }
    }

    .target-text {
      font-size: $font-size-14;
      font-weight: $font-weight-500;
      font-family: $font-family-base;
      line-height: $font-size-24;
      color: $color-tertiary;
    }
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

.nav-button-filled {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: $bg-color-light;
  font-size: $font-size-14;
  font-weight: $font-weight-400;
  line-height: 20px;
  color: $text-color;

  &:hover {
    cursor: pointer;
  }

  &.active {
    background-color: #e2f3fe;
    color: $text-color-active;
  }
}

.nav-button-text {
  font-size: $font-size-14;
  font-weight: $font-weight-500;
  line-height: 20px;
  color: $color-secondary;
  cursor: pointer;

  &.active {
    color: $text-color-active;
  }
}
