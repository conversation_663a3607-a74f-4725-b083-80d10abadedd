import React, { memo, useMemo, useRef, useState, useCallback } from 'react'
import cx from 'classnames'
import styled from 'styled-components'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import Tooltip from '@hi-ui/tooltip'
import { InfoCircleOutlined } from '@hi-ui/icons'
import { bigFieldIcon as BigFieldIcon } from '@/views/qms/billboard/assets'
import { CARD_MAX_WIDTH } from '@/views/qms/billboard/constant'
import type { ContentParams } from './BoardLine'
import { NavButtonText } from './NavButton'
import { RadiusCard, RiskTag, Divider } from './Widgets'
import BoardChart from './BoardChart'
import { orangeTheme } from '../config'
import { BarChartData } from '../interface'

import {
  PLACEHOLDER_STR,
  CARD_NAME,
  FONT_WEIGHT_BOLD,
  FONT_SIZE_MEDIUM,
  CARD_STYLE,
  DATA_PRODUCTION_TIME_STYLE,
} from '../constant'
import '../index.scss'

const TitleHandleWapper = styled.div`
  padding-top: 12px;
  padding-bottom: 12px;
  font-weight: ${FONT_WEIGHT_BOLD};
  font-size: ${FONT_SIZE_MEDIUM};
  color: ${CARD_STYLE.TITLE_COLOR};
`
interface MfgDiProps {
  cardName: string
  value: string
  target: string
  achieve: number
}

const MfgDiHeader = memo((props: MfgDiProps) => {
  const { cardName, value, target, achieve: propsAchieve } = props || {}
  const achieve = useMemo(() => {
    if (propsAchieve > 0) {
      return propsAchieve
    } else if (propsAchieve === 0 && (target ?? PLACEHOLDER_STR) === PLACEHOLDER_STR) {
      return -1
    }
    return propsAchieve
  }, [propsAchieve, target])
  return (
    <div className="mfgdi-header flex items-center gap-8">
      <div className="flex-1">
        <p className="card-name">{cardName}</p>
        <p className="flex items-center justify-between gap-8">
          <span
            className={cx('value-text', {
              'value-text-success': value !== PLACEHOLDER_STR && achieve > 0,
              'value-text-fail': value !== PLACEHOLDER_STR && achieve === 0,
            })}
          >
            {value ?? PLACEHOLDER_STR}
          </span>
          <span className="target-text">
            {'目标'}
            {target ?? PLACEHOLDER_STR}
          </span>
        </p>
      </div>
    </div>
  )
})

MfgDiHeader.displayName = 'MfgDiHeader'

const QualityPhone: React.FC<ContentParams> = ({ activeNavBar }) => {
  // mfg di
  const indexNodeList = ['TR4', 'TR5', 'TR6']
  const remainDays = 14
  const [trPhase, setTrPhase] = useState<string>(indexNodeList[indexNodeList.length - 1])
  const isEmpty = false
  const tooltipText =
    'MFG DI(Manufacturing Defect Index，生产制造缺陷率)是衡量生产质量的指标，将生产过程中未解决的问题按问题严重程度加权计算得到的量化质量缺陷的指标，DI=Blocker问题个数*10 + Critical问题个数*3 + Major问题个数*1 + Minor问题个数*0.1'

  const boardName = CARD_NAME.MFG_DI
  const childRef = useRef(null)
  const currentBoardConfig = {
    name: 'DI 达标',
    theme: orangeTheme,
    ChartType: 'mfgdi',
  }
  const chartData: BarChartData = {
    chartData: [
      {
        name: 'xxx',
        dataList: [
          {
            name: '1',
            value: 123,
          },
          {
            name: '1',
            value: 133,
          },
        ],
      },
    ],
  }
  const handleClick = useCallback(
    (params) => {
      console.log('ddd ==> click', params, boardName)
    },
    [boardName]
  )

  const showTooltip = () => {
    return <div style={{ maxWidth: CARD_MAX_WIDTH, whiteSpace: 'wrap' }}>{tooltipText}</div>
  }
  return (
    <>
      <div className="prd-boardcell">
        <TitleHandleWapper className="flex justify-between items-center gap-10">
          <div className="flex justify-start items-center">
            <BigFieldIcon />
            <span className="ml-2">{CARD_NAME.MFG_DI}</span>
            <Tooltip title={showTooltip()} trigger="hover">
              <InfoCircleOutlined style={{ fontSize: 16, marginLeft: 4, color: '#5F6A7A' }} />
            </Tooltip>
            <div
              className="flex items-center gap-4 ml-8"
              style={{
                color: DATA_PRODUCTION_TIME_STYLE.color,
                fontWeight: DATA_PRODUCTION_TIME_STYLE.fontWeight,
              }}
            >
              <span>{activeNavBar.project}</span>
              <Divider width="1px" height="12px" />
              <span>{activeNavBar.factory}</span>
              <Divider width="1px" height="12px" />
              <span>
                距离{trPhase}评审
                <strong style={{ color: CARD_STYLE.ACTIVE_COLOR }} className="font-bold ml-4 mr-4">
                  {remainDays}
                </strong>
                天
              </span>
              <RiskTag className="ml-4" risk="high">
                高风险
              </RiskTag>
            </div>
          </div>
          <NavButtonText list={indexNodeList || []} setActive={setTrPhase} active={trPhase} />
        </TitleHandleWapper>
        <div className="flex gap-8">
          <RadiusCard style={{ flex: '1 1 45%' }}>
            <MfgDiHeader cardName={boardName} target={'50'} value={'81'} achieve={1} />
            <div className="flex-1 w-full">
              {isEmpty ? (
                <EmptyState
                  className="flex items-center justify-center"
                  title={intl.get('暂无数据')}
                  indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
                />
              ) : (
                <BoardChart
                  ref={childRef}
                  data={chartData}
                  currentBoardConfig={currentBoardConfig}
                  controlXText={[]}
                  width="100%"
                  height="314px"
                  onClick={handleClick}
                  boardName={''}
                  lastPeriod={''}
                />
              )}
            </div>
          </RadiusCard>
          <RadiusCard style={{ flex: '1 1 55%' }}>
            <div className="mfgdi-grid">
              <RadiusCard>
                <h3>问题总数</h3>
                <h4 className="text-2xl">16</h4>
              </RadiusCard>
              <RadiusCard>
                <h3>问题关闭率</h3>
                <h4 className="text-2xl">89%</h4>
              </RadiusCard>
            </div>
            <RadiusCard></RadiusCard>
          </RadiusCard>
        </div>
      </div>
    </>
  )
}
export default QualityPhone
