export interface TabListItem {
  title: string
  id: string
}
export interface ExpandItem {
  id: string
  title: string
}
export interface ProjectItem {
  projectCode: string
  withinTwoWeek: number
  remainDays: null | number
  nextTr: string
  marketDays: null | number
  indexNodeList: string[]
}
export interface ChartTheme {
  color: string[] | Record<string, string | string[]>
  icons?: string[]
}
// 通用图表数据项接口
export interface ChartDataItem {
  name: string
  value: number
  details?: string[]
}

// 通用图表数据结构接口
export interface ChartData<T extends ChartDataItem = ChartDataItem> {
  chartData: {
    name: string
    dataList: T[]
  }[]
}

// 饼图数据项接口
export interface PieChartDataItem extends ChartDataItem {
  details: string[]
}

// 柱状图数据项接口
export type BarChartDataItem = ChartDataItem

// 饼图和柱状图数据类型
export type PieChartData = ChartData<PieChartDataItem>
export type BarChartData = ChartData<BarChartDataItem>
