import { defaultChartLineConfig } from '@/views/qms/billboard/config'
import {
  CHART_AXIS_COLOR,
  CHART_TOOLTIP_TITLE_FONT_SIZE,
  CHART_TOOLTIP_TITLE_COLOR,
  CHART_TOOLTIP_COLOR,
} from '@/views/qms/billboard/constant'

function tooltipFormatter(params) {
  // 兼容单轴pie图
  params = Array.isArray(params) ? params : [params]
  let tooltipStr = '<div style="border-radius: 6px;padding: 0 4px;">'
  if (params[0].axisValueLabel) {
    tooltipStr += `<p style="font-weight: 600; font-size: ${CHART_TOOLTIP_TITLE_FONT_SIZE}; color: ${CHART_TOOLTIP_TITLE_COLOR};">${params[0].axisValueLabel}</p>`
  }
  params.forEach((item) => {
    const { value, name, seriesName, color } = item
    const mergeColor = typeof color === 'string' ? color : color?.colorStops[0]?.color ?? 'black'
    tooltipStr += `<div style="display: flex; font-size: 12px; justify-content: space-between; align-items: center; width: auto; margin-top: 8px;">
     <div style="width: 10px; height: 6px; border-radius: 1px; margin-bottom: 2px; background-color: ${mergeColor}; align-self: center;"></div>
     <div style="flex: 1; display: flex; align-items: center; color: ${CHART_TOOLTIP_COLOR}; margin: 0 14px 0 8px; text-align: left;"> ${
       seriesName || name
     }</div>
     <div style="flex: 2; color: ${CHART_TOOLTIP_TITLE_COLOR};font-weight: 600; text-align: right; margin-right: 14px;">
     ${value}
     </div>
     ${
       item.percent !== undefined
         ? `<div style="flex:1; text-align: right;><span style="color: ${CHART_TOOLTIP_COLOR};">
      占比
      ${item.percent}%
      </span></div>`
         : ''
     }</div>
    </div>`
  })
  tooltipStr += '</div>'
  return tooltipStr
}

export const getBarConfig = function (data): echarts.EChartsOption {
  console.log('ddd ==> mfgdi-getBarConfig', data)
  return {
    ...defaultChartLineConfig,
    title: {
      text: 'DI达标',
      left: -5,
      textStyle: {
        color: '#1F2733',
        fontFamily: 'PingFang SC',
        fontWeight: 500,
        fontSize: 14,
        lineHeight: 20,
      },
    },
    legend: {
      ...defaultChartLineConfig.legend,
      icon: 'rect',
    },
    grid: {
      ...defaultChartLineConfig.grid,
      top: 40 + 23,
    },
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      trigger: 'axis',
      formatter: tooltipFormatter,
      axisPointer: {
        type: 'line',
        z: -15,
        lineStyle: {
          color: '#DEE0E867',
          width: 40,
          type: 'solid',
        },
      },
    },
    xAxis: [
      {
        type: 'category',
        data: ['TR4A', 'TR5', 'TR6'],
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: CHART_AXIS_COLOR,
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: CHART_AXIS_COLOR,
          },
        },
        splitLine: {
          lineStyle: {
            color: '#EBEDF0A6',
          },
        },
      },
    ],
    series: [
      {
        name: '实际值',
        type: 'bar',
        color: '#0189FF',
        barWidth: 20,
        itemStyle: {
          borderRadius: [10, 10, 0, 0],
        },
        data: [120, 152, 200],
      },
      {
        name: '目标值',
        type: 'line',
        color: '#FF710E',
        showSymbol: true,
        symbolSize: [21, 2],
        symbol: 'rect',
        showAllSymbol: false,
        emphasis: {
          scale: 1,
        },
        lineStyle: {
          width: 0,
          opacity: 0,
        },
        data: [120, 132, 220],
      },
    ],
  }
}

export const getPieConfig = function (data): echarts.EChartsOption {
  console.log('ddd ==> mfgdi-getPieConfig', data)
  const result = (data.chartData.dataList || []).map((item) => {
    return { ...item, itemStyle: { color: item.color } }
  })

  return {
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      trigger: 'item',
      formatter: tooltipFormatter,
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['50%', '75%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: result,
      },
    ],
  }
}
