/**
 * 字符串工具函数
 */

/**
 * 首字母大写
 * @param str 输入字符串
 * @returns 首字母大写的字符串
 */
export const formatFirstToUpperCase = (str = ''): string => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰转短横线
 * @param str 驼峰字符串
 * @returns 短横线分隔的字符串
 */
export const camelToKebab = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * 短横线转驼峰
 * @param str 短横线分隔的字符串
 * @returns 驼峰字符串
 */
export const kebabToCamel = (str: string): string => {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 截断字符串
 * @param str 输入字符串
 * @param length 最大长度
 * @param suffix 后缀
 * @returns 截断后的字符串
 */
export const truncate = (str: string, length: number, suffix = '...'): string => {
  if (str.length <= length) return str
  return str.slice(0, length) + suffix
}

/**
 * 移除字符串中的HTML标签
 * @param str 包含HTML的字符串
 * @returns 纯文本字符串
 */
export const stripHtml = (str: string): string => {
  return str.replace(/<[^>]*>/g, '')
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @param chars 可选字符集
 * @returns 随机字符串
 */
export const randomString = (
  length: number,
  chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string => {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 检查字符串是否为空或只包含空白字符
 * @param str 输入字符串
 * @returns 是否为空
 */
export const isEmpty = (str: string | null | undefined): boolean => {
  return !str || str.trim().length === 0
}

/**
 * 安全的字符串模板替换
 * @param template 模板字符串
 * @param data 替换数据
 * @returns 替换后的字符串
 */
export const template = (template: string, data: Record<string, any>): string => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return data[key] !== undefined ? String(data[key]) : match
  })
}
