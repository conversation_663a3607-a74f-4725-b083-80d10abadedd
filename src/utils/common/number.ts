/**
 * 数字工具函数
 */

/**
 * 格式化数字为百分比
 * @param value 数值
 * @param decimals 小数位数
 * @returns 百分比字符串
 */
export const formatToPercentage = (value: number, decimals = 2): string => {
  if (isNaN(value)) return '0%'
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * 格式化大数字（K, M, B）
 * @param value 数值
 * @param decimals 小数位数
 * @returns 格式化后的字符串
 */
export const formatLargeNumber = (value: number, decimals = 1): string => {
  if (isNaN(value)) return '0'
  
  const abs = Math.abs(value)
  const sign = value < 0 ? '-' : ''
  
  if (abs >= 1e9) {
    return `${sign}${(abs / 1e9).toFixed(decimals)}B`
  } else if (abs >= 1e6) {
    return `${sign}${(abs / 1e6).toFixed(decimals)}M`
  } else if (abs >= 1e3) {
    return `${sign}${(abs / 1e3).toFixed(decimals)}K`
  }
  
  return value.toString()
}

/**
 * 添加千分位分隔符
 * @param value 数值
 * @returns 带千分位分隔符的字符串
 */
export const addThousandSeparator = (value: number | string): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0'
  
  return num.toLocaleString()
}

/**
 * 安全的数字转换
 * @param value 输入值
 * @param defaultValue 默认值
 * @returns 数字或默认值
 */
export const safeNumber = (value: any, defaultValue = 0): number => {
  const num = Number(value)
  return isNaN(num) ? defaultValue : num
}

/**
 * 限制数字在指定范围内
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 限制后的数值
 */
export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max)
}

/**
 * 生成指定范围内的随机数
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 * @returns 随机数
 */
export const randomNumber = (min: number, max: number, decimals = 0): number => {
  const random = Math.random() * (max - min) + min
  return decimals > 0 ? parseFloat(random.toFixed(decimals)) : Math.floor(random)
}

/**
 * 计算百分比变化
 * @param oldValue 旧值
 * @param newValue 新值
 * @returns 百分比变化
 */
export const calculatePercentageChange = (oldValue: number, newValue: number): number => {
  if (oldValue === 0) return newValue === 0 ? 0 : 100
  return ((newValue - oldValue) / oldValue) * 100
}

/**
 * 四舍五入到指定小数位
 * @param value 数值
 * @param decimals 小数位数
 * @returns 四舍五入后的数值
 */
export const roundTo = (value: number, decimals: number): number => {
  const factor = Math.pow(10, decimals)
  return Math.round(value * factor) / factor
}
