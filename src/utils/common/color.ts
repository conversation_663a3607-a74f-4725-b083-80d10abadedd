/**
 * 颜色工具函数
 */

/**
 * 十六进制转RGB
 * @param hex 十六进制颜色值
 * @returns RGB对象
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

/**
 * RGB转十六进制
 * @param r 红色值
 * @param g 绿色值
 * @param b 蓝色值
 * @returns 十六进制颜色值
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

/**
 * 调整颜色亮度
 * @param color 颜色值
 * @param amount 调整量 (-100 到 100)
 * @returns 调整后的颜色值
 */
export const adjustBrightness = (color: string, amount: number): string => {
  const rgb = hexToRgb(color)
  if (!rgb) return color

  const adjust = (value: number) => {
    const adjusted = value + (amount * 255) / 100
    return Math.max(0, Math.min(255, Math.round(adjusted)))
  }

  return rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b))
}

/**
 * 获取颜色的透明度版本
 * @param color 颜色值
 * @param alpha 透明度 (0-1)
 * @returns RGBA颜色值
 */
export const addAlpha = (color: string, alpha: number): string => {
  const rgb = hexToRgb(color)
  if (!rgb) return color

  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`
}

/**
 * 判断颜色是否为深色
 * @param color 颜色值
 * @returns 是否为深色
 */
export const isDarkColor = (color: string): boolean => {
  const rgb = hexToRgb(color)
  if (!rgb) return false

  // 使用相对亮度公式
  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
  return brightness < 128
}

/**
 * 获取对比色（黑色或白色）
 * @param color 背景颜色
 * @returns 对比色
 */
export const getContrastColor = (color: string): string => {
  return isDarkColor(color) ? '#FFFFFF' : '#000000'
}

/**
 * 生成随机颜色
 * @returns 随机十六进制颜色值
 */
export const randomColor = (): string => {
  return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
}

/**
 * 颜色混合
 * @param color1 颜色1
 * @param color2 颜色2
 * @param ratio 混合比例 (0-1)
 * @returns 混合后的颜色
 */
export const mixColors = (color1: string, color2: string, ratio: number): string => {
  const rgb1 = hexToRgb(color1)
  const rgb2 = hexToRgb(color2)
  
  if (!rgb1 || !rgb2) return color1

  const mix = (a: number, b: number) => Math.round(a + (b - a) * ratio)

  return rgbToHex(
    mix(rgb1.r, rgb2.r),
    mix(rgb1.g, rgb2.g),
    mix(rgb1.b, rgb2.b)
  )
}

/**
 * 生成颜色渐变数组
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param steps 步数
 * @returns 颜色数组
 */
export const generateGradient = (startColor: string, endColor: string, steps: number): string[] => {
  const colors: string[] = []
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1)
    colors.push(mixColors(startColor, endColor, ratio))
  }
  
  return colors
}
