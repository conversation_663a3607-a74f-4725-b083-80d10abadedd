/**
 * 格式化工具函数
 */

import dayjs from 'dayjs'

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (
  date: string | number | Date,
  format = 'YYYY-MM-DD HH:mm:ss'
): string => {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: string | number | Date): string => {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`
}

/**
 * 格式化持续时间
 * @param seconds 秒数
 * @returns 格式化后的持续时间
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.floor(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return minutes > 0 ? `${hours}小时${minutes}分` : `${hours}小时`
  }
}

/**
 * 格式化货币
 * @param amount 金额
 * @param currency 货币符号
 * @param decimals 小数位数
 * @returns 格式化后的货币字符串
 */
export const formatCurrency = (
  amount: number,
  currency = '¥',
  decimals = 2
): string => {
  if (isNaN(amount)) return `${currency}0.00`
  return `${currency}${amount.toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  })}`
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return ''
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  return phone
}

/**
 * 格式化身份证号
 * @param idCard 身份证号
 * @param mask 是否遮罩
 * @returns 格式化后的身份证号
 */
export const formatIdCard = (idCard: string, mask = true): string => {
  if (!idCard) return ''
  if (mask && idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }
  return idCard
}

/**
 * 格式化银行卡号
 * @param cardNumber 银行卡号
 * @param mask 是否遮罩
 * @returns 格式化后的银行卡号
 */
export const formatBankCard = (cardNumber: string, mask = true): string => {
  if (!cardNumber) return ''
  const cleaned = cardNumber.replace(/\D/g, '')
  
  if (mask && cleaned.length >= 8) {
    const start = cleaned.slice(0, 4)
    const end = cleaned.slice(-4)
    const middle = '*'.repeat(cleaned.length - 8)
    return `${start} ${middle} ${end}`.replace(/(.{4})/g, '$1 ').trim()
  }
  
  return cleaned.replace(/(.{4})/g, '$1 ').trim()
}
